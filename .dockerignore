# Git
.git
.gitignore
.gitattributes

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Documentation
README.md
*.md

# Environment files
.env
.env.local
.env.*.local

# Dependencies
node_modules
vendor

# IDE
.vscode
.idea
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Laravel specific
/storage/logs/*
/storage/framework/cache/*
/storage/framework/sessions/*
/storage/framework/views/*
/bootstrap/cache/*

# Build artifacts
/public/hot
/public/storage
/storage/*.key

# Testing
.phpunit.result.cache
/coverage

# Temporary files
*.tmp
*.temp
