version: '3.8'

services:
  # PHP-FPM Service
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: astroway-app
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - ./:/var/www/html
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - astroway-network
    depends_on:
      - db

  # Nginx Service
  nginx:
    image: nginx:alpine
    container_name: astroway-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./:/var/www/html
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/sites/:/etc/nginx/sites-available/
      - ./docker/nginx/sites/:/etc/nginx/sites-enabled/
      - ./docker/logs/nginx:/var/log/nginx/
    networks:
      - astroway-network
    depends_on:
      - app

  # MySQL Database Service
  db:
    image: mysql:8.0
    container_name: astroway-mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: astroway_db
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_USER: astroway_user
      MYSQL_PASSWORD: astroway_password
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"
    networks:
      - astroway-network

  # phpMyAdmin (Optional - for database management)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: astroway-phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: db
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: root_password
    ports:
      - "8080:80"
    networks:
      - astroway-network
    depends_on:
      - db

# Docker Networks
networks:
  astroway-network:
    driver: bridge

# Volumes
volumes:
  mysql_data:
    driver: local
