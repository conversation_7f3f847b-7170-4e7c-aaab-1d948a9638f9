#!/bin/bash

# Astroway Backend Docker Setup Script
echo "🚀 Setting up Astroway Backend with Docker..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p docker/logs/nginx
mkdir -p storage/logs
mkdir -p storage/framework/cache
mkdir -p storage/framework/sessions
mkdir -p storage/framework/views
mkdir -p bootstrap/cache

# Set proper permissions
echo "🔐 Setting proper permissions..."
chmod -R 755 storage
chmod -R 755 bootstrap/cache

# Copy environment file for Docker
echo "⚙️ Setting up environment configuration..."
if [ ! -f .env ]; then
    cp .env.docker .env
    echo "✅ Environment file created from .env.docker"
else
    echo "⚠️ .env file already exists. Please update database settings manually:"
    echo "   DB_HOST=db"
    echo "   DB_DATABASE=astroway_db"
    echo "   DB_USERNAME=astroway_user"
    echo "   DB_PASSWORD=astroway_password"
fi

# Build and start containers
echo "🐳 Building and starting Docker containers..."
docker-compose up -d --build

# Wait for MySQL to be ready and database to be imported
echo "⏳ Waiting for MySQL to be ready and database to be imported..."
echo "   This may take a few minutes as the db.sql file is being imported..."
sleep 60

# Install Composer dependencies
echo "📦 Installing Composer dependencies..."
docker-compose exec app composer install --no-dev --optimize-autoloader

# Generate application key
echo "🔑 Generating application key..."
docker-compose exec app php artisan key:generate

# Note: Database is automatically imported from db.sql file
echo "✅ Database imported from db.sql file automatically"

# Create storage link
echo "🔗 Creating storage symbolic link..."
docker-compose exec app php artisan storage:link

# Clear and cache configuration
echo "🧹 Clearing and caching configuration..."
docker-compose exec app php artisan config:clear
docker-compose exec app php artisan cache:clear
docker-compose exec app php artisan route:clear
docker-compose exec app php artisan view:clear
docker-compose exec app php artisan config:cache
docker-compose exec app php artisan route:cache

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📋 Service URLs:"
echo "   🌐 Application: http://localhost"
echo ""
echo "📊 Database Connection Details:"
echo "   Host: localhost (from host machine) or 'db' (from containers)"
echo "   Port: 3306"
echo "   Database: astroway_db"
echo "   Username: astroway_user"
echo "   Password: astroway_password"
echo "   Root Password: root_password"
echo ""
echo "🔧 Useful Commands:"
echo "   docker-compose up -d          # Start all services"
echo "   docker-compose down           # Stop all services"
echo "   docker-compose logs app       # View application logs"
echo "   docker-compose exec app bash  # Access application container"
echo ""
