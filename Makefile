# Astroway Backend Docker Management

.PHONY: help build up down restart logs shell db-shell composer artisan migrate seed backup

# Default target
help: ## Show this help message
	@echo "Astroway Backend Docker Management"
	@echo "=================================="
	@awk 'BEGIN {FS = ":.*##"; printf "\nUsage:\n  make \033[36m<target>\033[0m\n"} /^[a-zA-Z_-]+:.*?##/ { printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2 } /^##@/ { printf "\n\033[1m%s\033[0m\n", substr($$0, 5) } ' $(MAKEFILE_LIST)

##@ Development Commands

setup: ## Run initial setup (automated)
	@echo "🚀 Running automated setup..."
	./docker-setup.sh

build: ## Build Docker containers
	@echo "🏗️ Building containers..."
	docker-compose build

up: ## Start all services
	@echo "🚀 Starting services..."
	docker-compose up -d

down: ## Stop all services
	@echo "🛑 Stopping services..."
	docker-compose down

restart: ## Restart all services
	@echo "🔄 Restarting services..."
	docker-compose restart

logs: ## Show logs for all services
	@echo "📋 Showing logs..."
	docker-compose logs -f

logs-app: ## Show application logs
	@echo "📋 Showing application logs..."
	docker-compose logs -f app

logs-nginx: ## Show Nginx logs
	@echo "📋 Showing Nginx logs..."
	docker-compose logs -f nginx

logs-db: ## Show database logs
	@echo "📋 Showing database logs..."
	docker-compose logs -f db

##@ Container Access

shell: ## Access application container shell
	@echo "🐚 Accessing application container..."
	docker-compose exec app bash

db-shell: ## Access database shell
	@echo "🗄️ Accessing database shell..."
	docker-compose exec db mysql -u root -p

##@ Laravel Commands

composer-install: ## Install Composer dependencies
	@echo "📦 Installing Composer dependencies..."
	docker-compose exec app composer install --no-dev --optimize-autoloader

composer-update: ## Update Composer dependencies
	@echo "📦 Updating Composer dependencies..."
	docker-compose exec app composer update

artisan: ## Run Artisan command (usage: make artisan CMD="command")
	@echo "🎨 Running Artisan command: $(CMD)"
	docker-compose exec app php artisan $(CMD)

migrate: ## Run database migrations
	@echo "🗄️ Running migrations..."
	docker-compose exec app php artisan migrate --force

migrate-fresh: ## Fresh migration with seeding
	@echo "🗄️ Running fresh migrations..."
	docker-compose exec app php artisan migrate:fresh --seed --force

seed: ## Run database seeders
	@echo "🌱 Running seeders..."
	docker-compose exec app php artisan db:seed --force

key-generate: ## Generate application key
	@echo "🔑 Generating application key..."
	docker-compose exec app php artisan key:generate

storage-link: ## Create storage symbolic link
	@echo "🔗 Creating storage link..."
	docker-compose exec app php artisan storage:link

##@ Cache Management

cache-clear: ## Clear all caches
	@echo "🧹 Clearing caches..."
	docker-compose exec app php artisan cache:clear
	docker-compose exec app php artisan config:clear
	docker-compose exec app php artisan route:clear
	docker-compose exec app php artisan view:clear

cache-build: ## Build caches for production
	@echo "🏗️ Building caches..."
	docker-compose exec app php artisan config:cache
	docker-compose exec app php artisan route:cache
	docker-compose exec app php artisan view:cache

##@ Database Management

backup: ## Create database backup
	@echo "💾 Creating database backup..."
	docker-compose exec db mysqldump -u root -p astroway_db > ./docker/backups/backup_$(shell date +%Y%m%d_%H%M%S).sql

restore: ## Restore database from backup (usage: make restore FILE="backup_file.sql")
	@echo "🔄 Restoring database from $(FILE)..."
	docker-compose exec -T db mysql -u root -p astroway_db < ./docker/backups/$(FILE)

##@ Production Commands

prod-up: ## Start production environment
	@echo "🚀 Starting production environment..."
	docker-compose -f docker-compose.prod.yml up -d

prod-down: ## Stop production environment
	@echo "🛑 Stopping production environment..."
	docker-compose -f docker-compose.prod.yml down

prod-logs: ## Show production logs
	@echo "📋 Showing production logs..."
	docker-compose -f docker-compose.prod.yml logs -f

##@ Maintenance

clean: ## Clean up Docker resources
	@echo "🧹 Cleaning up Docker resources..."
	docker system prune -f
	docker volume prune -f

reset: ## Reset everything (DESTRUCTIVE)
	@echo "⚠️ Resetting everything..."
	docker-compose down -v
	docker-compose down --rmi all
	docker system prune -af

status: ## Show container status
	@echo "📊 Container status:"
	docker-compose ps

##@ Monitoring

monitor: ## Monitor resource usage
	@echo "📊 Monitoring resource usage..."
	docker stats

health: ## Check service health
	@echo "🏥 Checking service health..."
	@echo "Application: $$(curl -s -o /dev/null -w "%{http_code}" http://localhost || echo "DOWN")"
	@echo "phpMyAdmin: $$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080 || echo "DOWN")"
	@echo "Database: $$(docker-compose exec db mysqladmin ping -h localhost --silent && echo "UP" || echo "DOWN")"
