#!/bin/bash

# Import database script for Astroway Backend
echo "🗄️ Importing database from db.sql..."

# Check if db.sql exists
if [ ! -f "db.sql" ]; then
    echo "❌ db.sql file not found in current directory"
    exit 1
fi

# Check if MySQL container is running
if ! docker-compose ps db | grep -q "Up"; then
    echo "❌ MySQL container is not running. Please start it first with: docker-compose up -d"
    exit 1
fi

echo "⏳ Importing database... This may take a few minutes..."

# Import the database
docker-compose exec -T db mysql -u root -proot_password astroway_db < db.sql

if [ $? -eq 0 ]; then
    echo "✅ Database imported successfully!"
    echo ""
    echo "🔧 You may want to run these commands to optimize the application:"
    echo "   docker-compose exec app php artisan config:clear"
    echo "   docker-compose exec app php artisan cache:clear"
    echo "   docker-compose exec app php artisan route:clear"
else
    echo "❌ Database import failed!"
    exit 1
fi
