# Astroway Backend - Docker Setup

This document provides instructions for running the Astroway Backend PHP Laravel application using Docker with Nginx and MySQL.

## 🏗️ Architecture

The Docker setup includes:

- **PHP 8.1-FPM**: Laravel application container
- **Nginx**: Web server and reverse proxy
- **MySQL 8.0**: Database server
- **phpMyAdmin**: Database management interface (optional)

## 📋 Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- At least 2GB of available RAM
- Ports 80, 3306, and 8080 available

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)

```bash
# Run the automated setup script
./docker-setup.sh
```

### Option 2: Manual Setup

1. **Create environment file:**
   ```bash
   cp .env.docker .env
   ```

2. **Build and start containers:**
   ```bash
   docker-compose up -d --build
   ```

3. **Install dependencies:**
   ```bash
   docker-compose exec app composer install --no-dev --optimize-autoloader
   ```

4. **Generate application key:**
   ```bash
   docker-compose exec app php artisan key:generate
   ```

5. **Run migrations:**
   ```bash
   docker-compose exec app php artisan migrate --force
   ```

6. **Create storage link:**
   ```bash
   docker-compose exec app php artisan storage:link
   ```

## 🌐 Access Points

- **Application**: http://localhost
- **phpMyAdmin**: http://localhost:8080
- **MySQL**: localhost:3306

## 🗄️ Database Configuration

| Setting | Value |
|---------|-------|
| Host | `db` (from containers) or `localhost` (from host) |
| Port | 3306 |
| Database | astroway_db |
| Username | astroway_user |
| Password | astroway_password |
| Root Password | root_password |

## 🔧 Common Commands

### Container Management
```bash
# Start all services
docker-compose up -d

# Stop all services
docker-compose down

# Restart a specific service
docker-compose restart app

# View logs
docker-compose logs app
docker-compose logs nginx
docker-compose logs db

# Access container shell
docker-compose exec app bash
docker-compose exec db mysql -u root -p
```

### Laravel Commands
```bash
# Clear caches
docker-compose exec app php artisan cache:clear
docker-compose exec app php artisan config:clear
docker-compose exec app php artisan route:clear
docker-compose exec app php artisan view:clear

# Run migrations
docker-compose exec app php artisan migrate

# Seed database
docker-compose exec app php artisan db:seed

# Generate application key
docker-compose exec app php artisan key:generate

# Create storage link
docker-compose exec app php artisan storage:link
```

### Composer Commands
```bash
# Install dependencies
docker-compose exec app composer install

# Update dependencies
docker-compose exec app composer update

# Install specific package
docker-compose exec app composer require package/name
```

## 📁 Directory Structure

```
├── docker/
│   ├── nginx/
│   │   ├── nginx.conf          # Main Nginx configuration
│   │   └── sites/
│   │       └── default.conf    # Site-specific configuration
│   ├── php/
│   │   └── local.ini           # PHP configuration
│   ├── mysql/
│   │   └── my.cnf              # MySQL configuration
│   └── logs/                   # Log files
├── Dockerfile                  # PHP application container
├── docker-compose.yml          # Docker Compose configuration
├── .env.docker                 # Docker environment template
└── docker-setup.sh             # Automated setup script
```

## 🔧 Customization

### Environment Variables

Edit `.env` file to customize:
- Database credentials
- Application settings
- Third-party service configurations

### PHP Configuration

Modify `docker/php/local.ini` for:
- Memory limits
- Upload file sizes
- Execution timeouts

### Nginx Configuration

Edit `docker/nginx/sites/default.conf` for:
- Server names
- SSL certificates
- Custom headers

### MySQL Configuration

Modify `docker/mysql/my.cnf` for:
- Performance tuning
- Character sets
- Logging settings

## 🐛 Troubleshooting

### Common Issues

1. **Port conflicts:**
   ```bash
   # Check what's using the ports
   lsof -i :80
   lsof -i :3306
   lsof -i :8080
   ```

2. **Permission issues:**
   ```bash
   # Fix storage permissions
   docker-compose exec app chown -R www-data:www-data /var/www/html/storage
   docker-compose exec app chmod -R 755 /var/www/html/storage
   ```

3. **Database connection issues:**
   ```bash
   # Check if MySQL is running
   docker-compose ps db
   
   # Check MySQL logs
   docker-compose logs db
   ```

4. **Application errors:**
   ```bash
   # Check application logs
   docker-compose logs app
   
   # Check Laravel logs
   docker-compose exec app tail -f storage/logs/laravel.log
   ```

### Reset Everything

```bash
# Stop and remove all containers, networks, and volumes
docker-compose down -v

# Remove all images
docker-compose down --rmi all

# Start fresh
docker-compose up -d --build
```

## 🔒 Security Considerations

- Change default database passwords in production
- Use environment-specific `.env` files
- Enable SSL/TLS for production deployments
- Regularly update Docker images
- Implement proper backup strategies

## 📈 Performance Optimization

- Enable OPcache (already configured)
- Use Redis for caching and sessions
- Optimize MySQL configuration for your workload
- Use CDN for static assets
- Enable Nginx gzip compression (already configured)

## 🆘 Support

For issues related to:
- **Docker setup**: Check this documentation
- **Laravel application**: Refer to Laravel documentation
- **Astroway specific features**: Contact the application vendor
