{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1", "barryvdh/laravel-dompdf": "^2.1", "barryvdh/laravel-ide-helper": "2.13.0", "code-lts/laravel-fcm": "1.8.0", "google/cloud": "0.197.0", "google/protobuf": "^3.17", "grpc/grpc": "^1.38", "guzzlehttp/guzzle": "^7.6", "guzzlehttp/psr7": "^1.9", "illuminate/support": "10.0", "instamojo/instamojo-php": "^0.4.0", "iyzico/iyzipay-php": "~2.0.51", "kreait/firebase-php": "7.0.3", "kreait/laravel-firebase": "5.1.0", "laravel/framework": "10.0", "laravel/sanctum": "3.2", "laravel/tinker": "2.8", "laravel/ui": "4.2", "league/flysystem-aws-s3-v3": "^3.0", "livewire/livewire": "2.12.0", "mashape/unirest-php": "^3.0.4", "mcamara/laravel-localization": "^2.0", "mercadopago/dx-php": "~2.5.3", "mollie/mollie-api-php": "~2.52.0", "monolog/monolog": "3.3.1", "php-open-source-saver/jwt-auth": "2.1.0", "razorpay/razorpay": "~2.8.5", "stichoza/google-translate-php": "^5.1", "stripe/stripe-php": "~10.12.1", "taylanunutmaz/agora-token-builder": "^1.1", "tymon/jwt-auth": "^2.0", "wixel/gump": "^2.0.0", "yabacon/paystack-php": "^2.2", "yasserbelhimer/agora-access-token-generator": "^1.0"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "exclude-from-classmap": ["/vendor/mercadopago/dx-php/src/MercadoPago/Entity.php", "/vendor/rmccue/**"], "classmap": ["vendor-overrides/authorizenet/", "vendor-overrides/pagseguro/pagseguro-php-sdk/"], "files": ["vendor-overrides/mercadopago/Entity.php", "app/Support/app-helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}