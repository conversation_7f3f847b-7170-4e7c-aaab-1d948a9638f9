# Use PHP 8.2 FPM as base image
FROM php:8.2-fpm

# Set working directory
WORKDIR /var/www/html

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    libzip-dev \
    zip \
    unzip \
    libfreetype6-dev \
    libjpeg62-turbo-dev \
    libmcrypt-dev \
    libgd-dev \
    jpegoptim optipng pngquant gifsicle \
    vim \
    nano \
    supervisor \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install PHP extensions
RUN docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) \
        pdo_mysql \
        mbstring \
        exif \
        pcntl \
        bcmath \
        gd \
        zip \
        intl \
        soap

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Copy composer files first for better caching
COPY composer.json composer.lock* ./

# Install PHP dependencies first (this layer will be cached if composer files don't change)
# If lock file has compatibility issues, update it
RUN composer install --no-dev --no-scripts --no-autoloader --prefer-dist --no-interaction || \
    (composer update --no-dev --no-scripts --no-autoloader --prefer-dist --no-interaction && \
     composer install --no-dev --no-scripts --no-autoloader --prefer-dist --no-interaction)

# Copy application files
COPY . .

# Copy environment file
COPY .env.example .env

# Complete composer installation
RUN composer dump-autoload --no-dev --optimize

# Set proper permissions
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html/storage \
    && chmod -R 755 /var/www/html/bootstrap/cache

# Generate application key (only if not already set)
RUN php artisan key:generate --force

# Create symbolic link for storage (ignore if already exists)
RUN php artisan storage:link || true

# Expose port 9000 for PHP-FPM
EXPOSE 9000

# Start PHP-FPM
CMD ["php-fpm"]
